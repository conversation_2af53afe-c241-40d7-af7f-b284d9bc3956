package product

import (
	"pms-api/internal/models"
)

// ProductListRequest 定義產品列表查詢請求的結構
type ProductListRequest struct {
	// Page 頁碼，從1開始
	Page int32 `json:"page" validate:"min=1"`

	// PageSize 每頁數量，預設10，最大100
	PageSize int32 `json:"page_size" validate:"min=1,max=100"`

	// ProjectID 專案ID，必填
	ProjectID *uint32 `json:"project_id" validate:"required,min=1"`

	// Category 產品類別篩選，可選值：原品項、新增品項
	Category string `json:"category,omitempty" validate:"omitempty,oneof=原品項 新增品項"`

	// Brand 廠牌篩選
	Brand string `json:"brand,omitempty" validate:"max=50"`

	// SearchTerm 搜尋關鍵字，支援產品名稱、項次、廠商名稱的模糊搜尋
	SearchTerm string `json:"search_term,omitempty" validate:"max=100"`

	// IncludeDeleted 是否包含已刪除的產品
	IncludeDeleted bool `json:"include_deleted"`

	// SortBy 排序欄位，可選值：item_id、name、created_at
	SortBy string `json:"sort_by,omitempty" validate:"omitempty,oneof=item_id name created_at"`

	// SortDir 排序方向，可選值：asc、desc
	SortDir string `json:"sort_dir,omitempty" validate:"omitempty,oneof=asc desc"`
}

// ProductListResponse 定義產品列表查詢回應的結構
type ProductListResponse struct {
	// Total 總記錄數
	Total int64 `json:"total"`

	// Page 當前頁碼
	Page int32 `json:"page"`

	// PageSize 每頁數量
	PageSize int32 `json:"page_size"`

	// TotalPages 總頁數
	TotalPages int64 `json:"total_pages"`

	// List 產品列表
	List []*models.Product `json:"list"`
}
