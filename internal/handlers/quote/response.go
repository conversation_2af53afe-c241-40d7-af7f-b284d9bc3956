package quote

import (
	"pms-api/internal/models"
)

// APIErrorResponse 定義API錯誤回應的結構
type APIErrorResponse struct {
	// Message 錯誤訊息
	Message string `json:"message"`
}

// Response 定義報價回應的結構
type Response struct {
	// Quote 報價詳情
	Quote *models.Quote `json:"quote"`
}

// DetailResponse 定義報價詳情回應的結構
type DetailResponse struct {
	// QuoteDetail 報價詳細資訊
	QuoteDetail *models.QuoteDetail `json:"quote_detail"`
}

// ListResponse 定義報價列表回應的結構（舊版本）
type ListResponse struct {
	// Quotes 報價列表
	Quotes []*models.QuoteDetail `json:"quotes"`

	// Total 總數量
	Total int64 `json:"total"`

	// Page 當前頁碼
	Page int `json:"page"`

	// PageSize 每頁數量
	PageSize int `json:"page_size"`

	// TotalPages 總頁數
	TotalPages int32 `json:"total_pages"`
}

// QuoteListResponse 定義報價列表查詢回應的結構（新版本）
type QuoteListResponse struct {
	// Quotes 報價列表
	Quotes []*models.QuoteDetail `json:"quotes"`

	// Pagination 分頁資訊
	Pagination PaginationInfo `json:"pagination"`

	// Filters 篩選條件
	Filters QuoteListRequest `json:"filters"`
}

// PaginationInfo 定義分頁資訊結構
type PaginationInfo struct {
	// Page 當前頁碼
	Page int32 `json:"page"`

	// PageSize 每頁數量
	PageSize int32 `json:"page_size"`

	// Total 總記錄數
	Total int64 `json:"total"`

	// TotalPage 總頁數
	TotalPage int64 `json:"total_page"`
}

// StatsResponse 定義報價統計回應的結構
type StatsResponse struct {
	// Stats 報價統計資訊
	Stats *models.QuoteStats `json:"stats"`
}

// BatchReviewResponse 定義批量審核回應的結構
type BatchReviewResponse struct {
	// SuccessCount 成功數量
	SuccessCount int `json:"success_count"`

	// FailedCount 失敗數量
	FailedCount int `json:"failed_count"`

	// Message 操作結果訊息
	Message string `json:"message"`
}
